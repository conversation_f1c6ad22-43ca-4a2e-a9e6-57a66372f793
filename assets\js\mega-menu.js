/**
 * Mega Menu ve Dropdown Menu JavaScript for DmrThema
 * Mega menu ve normal dropdown menu islevsellik
 */

document.addEventListener('DOMContentLoaded', function() {

    // Mega menu elementlerini sec
    const megaMenuItems = document.querySelectorAll('.main-navigation ul li.has-mega-menu');

    // Normal dropdown menu elementlerini sec (has-mega-menu olmayan ama alt menusu olan)
    const dropdownMenuItems = document.querySelectorAll('.main-navigation ul li:not(.has-mega-menu)');

    // Mega menu islevsellik
    if (megaMenuItems.length > 0) {
        megaMenuItems.forEach(function(menuItem) {
            const subMenu = menuItem.querySelector('.sub-menu');
            const selectedPageId = menuItem.getAttribute('data-selected-page');

            // Mega menu blok sayfasi secili degilse ve alt menu varsa dropdown olarak goster
            if (!selectedPageId || selectedPageId === '0' || selectedPageId === '') {
                if (subMenu) {
                    // Mega menu stilini kaldir, dropdown stili ekle
                    menuItem.classList.remove('has-mega-menu');
                    menuItem.classList.add('has-dropdown-menu');
                    setupDropdownMenu(menuItem);
                }
                return;
            }

            if (!subMenu) {
                return; // Alt menu yoksa devam et
            }

            let hoverTimeout;

            // Menu item uzerine fare geldiginde
            menuItem.addEventListener('mouseenter', function() {
                // Timeout'u temizle (eger varsa)
                if (hoverTimeout) {
                    clearTimeout(hoverTimeout);
                    hoverTimeout = null;
                }

                // Diger acik mega menuleri kapat
                closeMegaMenus();

                // Bu mega menuyu ac - animasyon icin kisa gecikme
                requestAnimationFrame(function() {
                    menuItem.classList.add('mega-menu-active');
                });
            });

            // Menu item'dan fare ciktiginda
            menuItem.addEventListener('mouseleave', function() {
                // Kisa bir gecikme ile menu kapat
                // Bu gecikme fareyi mega menu uzerine tasima firsati verir
                hoverTimeout = setTimeout(function() {
                    menuItem.classList.remove('mega-menu-active');
                }, 300); // 300ms gecikme - daha uzun sure
            });

            // Sub menu uzerine fare geldiginde
            subMenu.addEventListener('mouseenter', function() {
                // Timeout'u temizle - menu acik kalsin
                if (hoverTimeout) {
                    clearTimeout(hoverTimeout);
                    hoverTimeout = null;
                }

                // Menu acik kalsin
                menuItem.classList.add('mega-menu-active');
            });

            // Sub menu'dan fare ciktiginda
            subMenu.addEventListener('mouseleave', function() {
                // Menu kapat
                hoverTimeout = setTimeout(function() {
                    menuItem.classList.remove('mega-menu-active');
                }, 300); // 300ms gecikme
            });
        });
    }

    // Normal dropdown menu islevsellik
    dropdownMenuItems.forEach(function(menuItem) {
        const subMenu = menuItem.querySelector('.sub-menu');
        if (subMenu) {
            setupDropdownMenu(menuItem);
        }
    });

    // Dropdown menu kurulum fonksiyonu
    function setupDropdownMenu(menuItem) {
        const subMenu = menuItem.querySelector('.sub-menu');
        if (!subMenu) return;

        let hoverTimeout;
        let isMouseInMenu = false;

        // Ana menu link'i
        const menuLink = menuItem.querySelector('a');

        // Menu link uzerine fare geldiginde
        menuLink.addEventListener('mouseenter', function(e) {
            e.stopPropagation();

            // Timeout'u temizle (eger varsa)
            if (hoverTimeout) {
                clearTimeout(hoverTimeout);
                hoverTimeout = null;
            }

            isMouseInMenu = true;

            // Diger acik dropdown menuleri kapat
            closeDropdownMenus();

            // Bu dropdown menuyu ac
            requestAnimationFrame(function() {
                menuItem.classList.add('dropdown-menu-active');
            });
        });

        // Menu link'den fare ciktiginda
        menuLink.addEventListener('mouseleave', function(e) {
            e.stopPropagation();

            // Mouse'un nereye gittigini kontrol et
            const relatedTarget = e.relatedTarget;

            // Eger mouse sub-menu'ye veya menu item'in herhangi bir parcasina gidiyorsa menu'yu acik birak
            if (relatedTarget && (
                subMenu.contains(relatedTarget) ||
                relatedTarget === subMenu ||
                menuItem.contains(relatedTarget)
            )) {
                return;
            }

            // Mouse koordinatlarini kontrol et - dropdown'a dogru gidiyorsa bekle
            const rect = subMenu.getBoundingClientRect();
            const mouseX = e.clientX;
            const mouseY = e.clientY;

            // Eger mouse dropdown'un yakin cevresindeyse bekle
            if (mouseX >= rect.left - 20 && mouseX <= rect.right + 20 &&
                mouseY >= rect.top - 20 && mouseY <= rect.bottom + 20) {
                // Kisa bir gecikme ile tekrar kontrol et
                setTimeout(function() {
                    if (!isMouseInMenu) {
                        isMouseInMenu = false;
                        hoverTimeout = setTimeout(function() {
                            if (!isMouseInMenu) {
                                menuItem.classList.remove('dropdown-menu-active');
                            }
                        }, 300);
                    }
                }, 100);
                return;
            }

            isMouseInMenu = false;

            // Gecikme ile menu kapat
            hoverTimeout = setTimeout(function() {
                if (!isMouseInMenu) {
                    menuItem.classList.remove('dropdown-menu-active');
                }
            }, 300); // 300ms gecikme
        });

        // Sub menu uzerine fare geldiginde
        subMenu.addEventListener('mouseenter', function(e) {
            e.stopPropagation();

            // Timeout'u temizle
            if (hoverTimeout) {
                clearTimeout(hoverTimeout);
                hoverTimeout = null;
            }

            isMouseInMenu = true;

            // Menu acik kalsin
            menuItem.classList.add('dropdown-menu-active');
        });

        // Sub menu'dan fare ciktiginda
        subMenu.addEventListener('mouseleave', function(e) {
            e.stopPropagation();

            const relatedTarget = e.relatedTarget;

            // Eger mouse ana menu'ye geri donuyorsa menu'yu acik birak
            if (relatedTarget && (menuItem.contains(relatedTarget) && !subMenu.contains(relatedTarget))) {
                return;
            }

            isMouseInMenu = false;

            // Menu kapat
            hoverTimeout = setTimeout(function() {
                menuItem.classList.remove('dropdown-menu-active');
            }, 200);
        });

        // Alt menulerin alt menuleri icin de ayni islemi yap
        const nestedSubMenus = subMenu.querySelectorAll('li');
        nestedSubMenus.forEach(function(nestedMenuItem) {
            const nestedSubMenu = nestedMenuItem.querySelector('.sub-menu');
            if (nestedSubMenu) {
                setupNestedDropdown(nestedMenuItem);
            }
        });
    }

    // Ic ice dropdown menu kurulum fonksiyonu
    function setupNestedDropdown(menuItem) {
        const subMenu = menuItem.querySelector('.sub-menu');
        if (!subMenu) return;

        let hoverTimeout;

        menuItem.addEventListener('mouseenter', function() {
            if (hoverTimeout) {
                clearTimeout(hoverTimeout);
                hoverTimeout = null;
            }

            // Ayni seviyedeki diger nested menuleri kapat
            const siblings = menuItem.parentNode.querySelectorAll('li.nested-dropdown-active');
            siblings.forEach(function(sibling) {
                if (sibling !== menuItem) {
                    sibling.classList.remove('nested-dropdown-active');
                }
            });

            menuItem.classList.add('nested-dropdown-active');
        });

        menuItem.addEventListener('mouseleave', function() {
            hoverTimeout = setTimeout(function() {
                menuItem.classList.remove('nested-dropdown-active');
            }, 150);
        });

        subMenu.addEventListener('mouseenter', function() {
            if (hoverTimeout) {
                clearTimeout(hoverTimeout);
                hoverTimeout = null;
            }
            menuItem.classList.add('nested-dropdown-active');
        });

        subMenu.addEventListener('mouseleave', function() {
            menuItem.classList.remove('nested-dropdown-active');
        });
    }

    // Tum mega menuleri kapat
    function closeMegaMenus() {
        megaMenuItems.forEach(function(item) {
            item.classList.remove('mega-menu-active');
        });
    }

    // Tum dropdown menuleri kapat
    function closeDropdownMenus() {
        const allDropdownItems = document.querySelectorAll('.main-navigation ul li.dropdown-menu-active, .main-navigation ul li.nested-dropdown-active');
        allDropdownItems.forEach(function(item) {
            item.classList.remove('dropdown-menu-active', 'nested-dropdown-active');
        });
    }

    // Sayfa uzerinde baska bir yere tiklandiginda menuleri kapat
    document.addEventListener('click', function(e) {
        // Tiklanilan element menu icinde degilse
        let isInsideMenu = false;

        const allMenuItems = document.querySelectorAll('.main-navigation ul li');
        allMenuItems.forEach(function(menuItem) {
            if (menuItem.contains(e.target)) {
                isInsideMenu = true;
            }
        });

        if (!isInsideMenu) {
            closeMegaMenus();
            closeDropdownMenus();
        }
    });

    // ESC tusuna basildiginda menuleri kapat
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape') {
            closeMegaMenus();
            closeDropdownMenus();
        }
    });

});
