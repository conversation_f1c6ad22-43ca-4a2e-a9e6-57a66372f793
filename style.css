/*
Theme Name: DmrThema
Author: Cline
Description: DmrThema için <PERSON> olarak geliştirilmiş bir tema.
Version: 1.0
*/

html {
    height: 100%;
}

body {
    font-family: Arial, sans-serif;
    margin: 0;
    padding: 0;
    min-height: 100%;
    display: flex;
    flex-direction: column;
    overflow-x: hidden; /* <PERSON><PERSON>y taşmayı engelle */
}

main {
    flex: 1 0 auto;
}

.container {
    width: 75%;
    margin: 0 auto;
    max-width: 1200px; /* Maksimum genislik siniri */
}

/* Header Stilleri */
.site-header {
    border-bottom: 1px solid #e0e0e0;
}

.header-top .container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 30px 0;
}

.logo a {
    text-decoration: none;
    color: #ff6000;
    font-size: 28px;
    font-weight: bold;
    display: block;
}

.site-logo {
    max-height: 60px;
    width: auto;
    display: block;
}

.search-form-container {
    flex-grow: 1;
    margin: 0 30px;
}

.header-right {
    display: flex;
    align-items: center;
    gap: 20px;
    min-height: 45px; /* Avatar yuksekligi ile uyumlu */
}

.search-form {
    display: flex;
    width: 100%;
}

.search-form label {
    flex-grow: 1;
}

.search-field {
    width: 100%;
    padding: 15px;
    border: 1px solid #ccc;
    border-right: none;
    border-radius: 5px 0 0 5px;
}

.search-submit {
    padding: 15px 20px;
    border: 1px solid #ff6000;
    background-color: #ff6000;
    color: white;
    cursor: pointer;
    border-radius: 0 5px 5px 0;
}

/* User Actions Genel Stilleri */
.user-actions {
    display: flex;
    align-items: center;
    padding-top: 5px;
}

/* Login Button Stilleri */
.login-button {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 10px 15px;
    background-color: #ff6000;
    color: white;
    text-decoration: none;
    border-radius: 8px;
    font-weight: 500;
    font-size: 14px;
    transition: all 0.3s ease;
    border: 2px solid #ff6000;
}

.login-button:hover {
    background-color: #e55500;
    border-color: #e55500;
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(255, 96, 0, 0.3);
}

.login-icon {
    width: 16px;
    height: 16px;
    flex-shrink: 0;
}

.login-text {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    line-height: 1.2;
}

.login-main {
    font-size: 14px;
    font-weight: 500;
}

.login-sub {
    font-size: 11px;
    opacity: 0.9;
    font-weight: 400;
}

/* User Avatar ve Dropdown Stilleri */
.user-profile-dropdown {
    position: relative;
    display: inline-block;
}

.user-avatar-button {
    background: none;
    border: none;
    cursor: pointer;
    padding: 0;
    border-radius: 50%;
    position: relative;
}

.user-avatar-button:focus {
    outline: 2px solid #ff6000;
    outline-offset: 2px;
}

.user-avatar-circle {
    width: 45px;
    height: 45px;
    border-radius: 50%;
    overflow: hidden;
    border: 2px solid #e0e0e0;
    background-color: #f8f9fa;
}

.user-avatar-img {
    width: 100% !important;
    height: 100% !important;
    max-width: none !important;
    object-fit: cover;
    display: block;
}

.user-dropdown-menu {
    position: absolute;
    top: calc(100% + 8px);
    right: 0;
    background: white;
    border: 1px solid #e0e0e0;
    border-radius: 12px;
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
    min-width: 220px;
    z-index: 1000;
    display: none;
}

.user-dropdown-menu.active {
    display: block;
}

.user-info {
    padding: 16px;
    border-bottom: 1px solid #f0f0f0;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-radius: 12px 12px 0 0;
}

.user-name {
    display: block;
    font-weight: 600;
    color: #333;
    font-size: 14px;
    margin-bottom: 4px;
}

.user-email {
    display: block;
    color: #666;
    font-size: 12px;
    opacity: 0.8;
}

.user-menu-items {
    padding: 8px 0;
}

.user-menu-item {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 12px 16px;
    color: #333;
    text-decoration: none;
    font-size: 14px;
    font-weight: 500;
    border-left: 3px solid transparent;
}

.user-menu-item:hover {
    background-color: rgba(248, 249, 250, 0.6);
}

.user-menu-item .menu-icon {
    width: 16px;
    height: 16px;
    flex-shrink: 0;
    opacity: 0.7;
}

.user-menu-item.logout {
    border-top: 1px solid #f0f0f0;
    color: #dc3545;
    margin-top: 4px;
}

.user-menu-item.logout:hover {
    background-color: rgba(255, 245, 245, 0.6);
}

/* WooCommerce Avatar Koruma - WooCommerce img kurallarini etkisiz hale getir */
.woocommerce .user-avatar-img,
.woocommerce-page .user-avatar-img {
    height: 100% !important;
    max-width: none !important;
    width: 100% !important;
}

.woocommerce .user-avatar-circle,
.woocommerce-page .user-avatar-circle {
    width: 45px !important;
    height: 45px !important;
}

/* Education Panel Stilleri */
.education-panel-button {
    display: flex;
    align-items: center;
    gap: 12px;
    text-decoration: none;
    color: #333;
    padding: 10px 20px;
    border: 2px solid #e0e0e0;
    border-radius: 12px;
    background-color: #ffffff;
    transition: all 0.3s ease;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.education-panel-button:hover {
    background-color: #fff;
    border-color: #ff6000;
    box-shadow: 0 4px 8px rgba(255, 96, 0, 0.15);
    transform: translateY(-1px);
}

.education-panel-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 25px;
    height: 25px;
    background-color: #ff6000;
    border-radius: 5px;
    color: white;
    flex-shrink: 0;
    transition: transform 0.3s ease;
}

.education-panel-text {
    display: flex;
    flex-direction: column;
    gap: 2px;
}

.education-panel-main {
    font-weight: 600;
    font-size: 14px;
    color: #333;
    line-height: 1.2;
}

.education-panel-sub {
    font-size: 11px;
    color: #666;
    line-height: 1.2;
}

/* Cart Stilleri */
.cart a {
    text-decoration: none;
    color: #333;
    padding: 10px 15px;
    border: 1px solid #ccc;
    border-radius: 5px;
    display: flex;
    align-items: center;
    justify-content: center;
    white-space: nowrap;
}

/* Sepet Butonu Stilleri */
.cart-button {
    background-color: #f0f0f0;
    border: 1px solid #ccc;
    border-radius: 5px;
    padding: 10px 15px;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 8px;
    color: #333;
    font-size: 14px;
    transition: all 0.3s ease;
}

.cart-button:hover {
    background-color: #e0e0e0;
    border-color: #999;
}

.cart-icon {
    width: 20px;
    height: 20px;
}

.cart-count {
    background-color: #ff6000;
    color: white;
    border-radius: 50%;
    padding: 2px 6px;
    font-size: 12px;
    font-weight: bold;
    min-width: 18px;
    text-align: center;
    line-height: 1.2;
}

.header-bottom {
    background-color: #F8F8F8;
    position: relative;
    z-index: 999; /* Mega menünün altında ama border görünür olsun */
}

.header-bottom .container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0;
}

.main-navigation ul {
    display: flex;
    justify-content: flex-start; /* Elemanları sola hizalar */
    list-style: none;
    margin: 0;
    padding: 0;
}

.main-navigation ul li {
    margin-right: 20px;
}

.main-navigation ul li a {
    display: block;
    padding: 20px 15px;
    text-decoration: none;
    color: #333;
    font-weight: bold;
}

.main-navigation ul li a:hover {
    background-color: #e0e0e0;
}

/* Mega Menü Stilleri */
.main-navigation ul li.has-mega-menu {
    position: static; /* Konumlandırmayı header'a göre yapmak için */
}

/* Mega Menu Sub-menu Stilleri */
.main-navigation ul li.has-mega-menu .sub-menu {
    display: none;
    position: absolute;
    left: 0; /* Tam genişlik için */
    right: 0; /* Tam genişlik için */
    width: auto; /* Sol ve sağa göre otomatik genişlik */
    top: calc(100% + 1px); /* Header-bottom'un border-bottom'una tam yapışık */
    background-color: #fff;
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
    padding: 20px 0;
    z-index: 1000; /* Yeterince yüksek z-index */
    margin-top: 0; /* Boşluk kaldırıldı */
    opacity: 0;
    transform: translateY(-10px);
    transition: opacity 0.3s ease, transform 0.3s ease;
    pointer-events: none; /* Animasyon sirasinda tiklanabilirlik engelle */
}

/* JavaScript ile kontrol edilecek - CSS hover kaldiriliyor */
.main-navigation ul li.mega-menu-active > .sub-menu {
    display: block;
    opacity: 1;
    transform: translateY(0);
    pointer-events: auto; /* Tiklanabilirlik aktif */
}

/* Dropdown Menu Stilleri */
.main-navigation ul li.has-dropdown-menu,
.main-navigation ul li:not(.has-mega-menu) {
    position: relative;
}

/* Normal Dropdown Sub-menu Stilleri */
.main-navigation ul li:not(.has-mega-menu) .sub-menu {
    display: none;
    position: absolute;
    left: 0;
    top: calc(100% - 2px); /* Ana menu ile ust uste bindir */
    background-color: #fff;
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
    border: 1px solid #e0e0e0;
    border-radius: 4px;
    min-width: 200px;
    z-index: 1000;
    opacity: 0;
    transform: translateY(-10px);
    transition: opacity 0.3s ease, transform 0.3s ease;
    pointer-events: none;
    padding: 8px 0;
}

/* Dropdown menu ile ana menu arasinda gorunmez kopru - daha buyuk */
.main-navigation ul li:not(.has-mega-menu) .sub-menu::before {
    content: '';
    position: absolute;
    top: -15px; /* Daha buyuk kopru */
    left: -10px; /* Sola da genislet */
    right: -10px; /* Saga da genislet */
    height: 15px; /* Daha yuksek kopru */
    background: transparent;
    z-index: 1001;
}

/* Ana menu item'a hover durumunda padding ekle */
.main-navigation ul li:not(.has-mega-menu):hover > a {
    padding-bottom: 25px !important; /* Alt tarafa padding ekle */
    margin-bottom: -5px; /* Margin ile dengeyi koru */
}

/* Dropdown menu aktif oldugunda ana menu item'in hover alanini genislet */
.main-navigation ul li:not(.has-mega-menu).dropdown-menu-active > a {
    position: relative;
    z-index: 1002;
}

.main-navigation ul li:not(.has-mega-menu).dropdown-menu-active > a::after {
    content: '';
    position: absolute;
    top: 100%;
    left: -10px;
    right: -10px;
    height: 20px;
    background: transparent;
    z-index: 1001;
}

/* Dropdown menu aktif durumu */
.main-navigation ul li.dropdown-menu-active > .sub-menu {
    display: block;
    opacity: 1;
    transform: translateY(0);
    pointer-events: auto;
}

/* Dropdown menu linkleri */
.main-navigation ul li:not(.has-mega-menu) .sub-menu li {
    width: 100%;
    margin: 0;
}

.main-navigation ul li:not(.has-mega-menu) .sub-menu li a {
    display: block;
    padding: 10px 15px !important;
    color: #333 !important;
    font-weight: normal !important;
    text-decoration: none;
    border-bottom: 1px solid #f0f0f0;
    transition: all 0.3s ease;
}

.main-navigation ul li:not(.has-mega-menu) .sub-menu li:last-child a {
    border-bottom: none;
}

.main-navigation ul li:not(.has-mega-menu) .sub-menu li a:hover {
    background-color: #f8f9fa !important;
    color: #ff6000 !important;
    padding-left: 20px !important;
}

/* Nested Dropdown Stilleri */
.main-navigation ul li:not(.has-mega-menu) .sub-menu li {
    position: relative;
}

.main-navigation ul li:not(.has-mega-menu) .sub-menu li .sub-menu {
    left: 100%;
    top: 0;
    margin-left: 1px;
}

.main-navigation ul li.nested-dropdown-active > .sub-menu {
    display: block;
    opacity: 1;
    transform: translateY(0);
    pointer-events: auto;
}

/* Alt menu gostergesi (ok) */
.main-navigation ul li:not(.has-mega-menu) .sub-menu li.menu-item-has-children > a::after {
    content: '▶';
    float: right;
    font-size: 10px;
    color: #999;
    transition: transform 0.3s ease;
}

.main-navigation ul li:not(.has-mega-menu) .sub-menu li.nested-dropdown-active > a::after {
    transform: rotate(90deg);
}

.sub-menu .sub-menu-container {
    width: 90%; /* Container'a uygun genişlik */
    max-width: 1200px; /* Ana container ile ayni maksimum genislik */
    margin: 0 auto;
    display: block;
    padding: 0 10px; /* Daha az kenar boşlukları */
    background: white;
    border-radius: 0;
}

.sub-menu .sub-menu-container > li {
    width: 100%; /* Tek sütun için tam genişlik */
    padding-right: 0;
    box-sizing: border-box;
}

.sub-menu li {
    width: 100%;
}

.sub-menu ul li a {
    padding: 8px 0 !important;
    font-weight: normal !important;
    color: #666 !important;
}

.sub-menu ul li a:hover {
    color: #ff6000 !important;
    background-color: transparent !important;
}

.sub-menu .sub-menu-container > li > a {
    font-weight: bold !important;
    color: #333 !important;
}

.sub-menu .sub-menu-container > li ul {
    list-style: none;
    padding-left: 0;
}

/* Mega Menu Sayfa Icerigi Stilleri - Sadece Konteyner */
.mega-menu-page-content {
    padding: 20px;
    background: white;
}

/* Liste ogelerinin dikey siralanmasi */
.mega-menu-page-content ul,
.mega-menu-page-content ol {
    display: block !important;
    list-style-position: inside !important;
}

.mega-menu-page-content li {
    display: list-item !important;
    width: 100% !important;
    float: none !important;
    clear: both !important;
}

/* Mega menu sayfa icerigindeki paragraph ve link stilleri */
.mega-menu-page-content p {
    margin: 0 !important;
    padding: 0 !important;
    line-height: inherit !important;
}

.mega-menu-page-content a {
    margin: 0 !important;
    padding: 0 !important;
    line-height: inherit !important;
    text-decoration: none !important;
    color: inherit !important;
    background-color: transparent !important;
    font-weight: inherit !important;
    font-size: inherit !important;
    font-family: inherit !important;
}

.mega-menu-page-content a:hover {
    color: #ff6000 !important;
    background-color: transparent !important;
}

.mega-menu-page-content a:active,
.mega-menu-page-content a:focus,
.mega-menu-page-content a:visited {
    background-color: transparent !important;
}

/* Mega menu icerigindeki tum elementlerin margin ve padding sifirlanmasi */
.mega-menu-page-content * {
    margin: 0 !important;
    padding: 0 !important;
}

/* Sadece container'in kendi padding'i korunur */
.mega-menu-page-content {
    padding: 20px !important;
}









/* Renkli Şerit */
.header-bottom::after {
    content: '';
    display: block;
    height: 4px;
    background: linear-gradient(to right,
        #ff6000 0%,
        #ff6000 15%,
        #41b6e6 25%,
        #41b6e6 35%,
        #8a3ffc 45%,
        #8a3ffc 55%,
        #4caf50 65%,
        #4caf50 75%,
        #5e35b1 85%,
        #5e35b1 100%);
}

/* Sidebar acikken body scroll'u engelle */
body.cart-sidebar-open {
    overflow: hidden !important;
}

/* HTML elementine de overflow hidden ekle */
html.cart-sidebar-open {
    overflow: hidden !important;
}

/* Sepet Sidebar Overlay */
.cart-sidebar-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    background-color: rgba(0, 0, 0, 0.5);
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
    z-index: 999998;
}

.cart-sidebar-overlay.active {
    opacity: 1;
    visibility: visible;
}

/* Sepet Sidebar */
.cart-sidebar {
    position: fixed;
    top: 0;
    right: -400px;
    width: 400px;
    height: 100vh;
    z-index: 1000000;
    transition: right 0.3s ease;
}

.cart-sidebar.active {
    right: 0;
}

.cart-sidebar-content {
    background-color: white;
    height: 100%;
    box-shadow: -2px 0 10px rgba(0, 0, 0, 0.1);
    display: flex;
    flex-direction: column;
    position: relative;
    overflow: hidden; /* Footer tasmasini onle */
}

.cart-sidebar-header {
    padding: 20px;
    border-bottom: 1px solid #e0e0e0;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.cart-sidebar-header h3 {
    margin: 0;
    font-size: 18px;
    color: #333;
}

.cart-sidebar-close {
    background: none;
    border: none;
    cursor: pointer;
    padding: 5px;
    color: #666;
    transition: color 0.3s ease;
}

.cart-sidebar-close:hover {
    color: #333;
}

.cart-sidebar-body {
    flex: 1;
    overflow: hidden; /* Scroll widget_shopping_cart_content'te olacak */
    padding: 20px;
    padding-bottom: 0;
    display: flex;
    flex-direction: column;
    min-height: 0; /* Flex child'in kuculebilmesi icin */
    max-height: calc(100vh - 140px); /* Header ve footer icin alan birak */
}

.cart-sidebar-footer {
    padding: 20px;
    border-top: 1px solid #e0e0e0;
    background-color: #f8f9fa;
    margin-top: auto;
}

.cart-footer-info {
    text-align: center;
}

.cart-footer-info p {
    margin: 0;
    color: #666;
}

.cart-footer-info small {
    font-size: 12px;
}

/* Mini Cart Stilleri */
.widget_shopping_cart_content {
    font-size: 14px;
    position: relative;
    height: 100%;
    display: flex;
    flex-direction: column;
    min-height: 0; /* Flex child'in kuculebilmesi icin */
    flex: 1; /* Parent'in tum alanini kullan */
    overflow: hidden; /* Child elementlerin tasmasini onle */
}

.widget_shopping_cart_content .woocommerce-mini-cart {
    list-style: none !important;
    padding: 0 !important;
    margin: 0 !important;
    flex: 1;
    overflow-y: auto;
    overflow-x: hidden;
    max-height: calc(100vh - 320px); /* Header, footer ve padding icin alan */
    padding-bottom: 20px !important; /* Footer icin yeterli alan birak */

}



.widget_shopping_cart_content .woocommerce-mini-cart-item {
    display: flex;
    align-items: flex-start;
    padding: 12px 0 !important;
    border-bottom: 1px solid #f0f0f0;
    position: relative;
    z-index: 1;
}

.widget_shopping_cart_content .woocommerce-mini-cart-item:last-child {
    border-bottom: none;
}

/* Urun resimleri - tutarli boyut */
html body .cart-sidebar .widget_shopping_cart_content .woocommerce-mini-cart-item img {
    width: 50px !important;
    height: 50px !important;
    object-fit: cover !important;
    border-radius: 5px !important;
    margin-right: 12px !important;
    flex-shrink: 0 !important;
}

/* Remove butonu - tutarli pozisyon */
html body .cart-sidebar .widget_shopping_cart_content .woocommerce-mini-cart-item .remove {
    position: absolute !important;
    top: 10px !important;
    right: 0 !important;
    color: #ff4444 !important;
    text-decoration: none !important;
    font-weight: bold !important;
    font-size: 16px !important;
    width: 20px !important;
    height: 20px !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    border-radius: 50% !important;
    background: rgba(255, 68, 68, 0.1) !important;
}

html body .cart-sidebar .widget_shopping_cart_content .woocommerce-mini-cart-item .remove:hover {
    color: #cc0000 !important;
    background: rgba(204, 0, 0, 0.1) !important;
}

/* Urun bilgileri - tutarli gorunum */
html body .cart-sidebar .widget_shopping_cart_content .woocommerce-mini-cart-item .product-details {
    flex: 1 !important;
    padding-right: 25px !important;
}

html body .cart-sidebar .widget_shopping_cart_content .woocommerce-mini-cart-item .product-name {
    display: block !important;
    text-decoration: none !important;
    color: #333 !important;
    font-weight: 500 !important;
    margin-bottom: 5px !important;
    line-height: 1.3 !important;
}

html body .cart-sidebar .widget_shopping_cart_content .woocommerce-mini-cart-item .product-name:hover {
    color: #ff6000 !important;
}

html body .cart-sidebar .widget_shopping_cart_content .woocommerce-mini-cart-item .product-info {
    margin-top: 5px !important;
}

html body .cart-sidebar .widget_shopping_cart_content .woocommerce-mini-cart-item .quantity {
    color: #666 !important;
    font-size: 12px !important;
    display: block !important;
}

/* Cart Footer Stilleri - Normal flow */
.widget_shopping_cart_content .cart-footer {
    margin-top: auto;
    padding: 20px 0;
    background-color: #fff;
    border-top: 2px solid #e9ecef;
    box-shadow: 0 -4px 12px rgba(0, 0, 0, 0.1);
    flex-shrink: 0; /* Footer'in kuculmamasini sagla */
}

/* Toplam fiyat */
.widget_shopping_cart_content .woocommerce-mini-cart__total {
    padding: 0 0 15px 0;
    margin: 0;
    font-weight: bold;
    font-size: 16px;
    text-align: center;
    border-bottom: 1px solid #dee2e6;
    margin-bottom: 15px;
}

.widget_shopping_cart_content .woocommerce-mini-cart__total .amount {
    color: #ff6000;
    font-size: 18px;
}

/* Butonlar */
.widget_shopping_cart_content .woocommerce-mini-cart__buttons {
    margin: 0;
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.widget_shopping_cart_content .woocommerce-mini-cart__buttons .button {
    display: block;
    width: 90%;
    text-align: center;
    padding: 12px 15px;
    text-decoration: none;
    border-radius: 5px;
    transition: all 0.3s ease;
    font-weight: 500;
    border: none;
    cursor: pointer;
}

.widget_shopping_cart_content .woocommerce-mini-cart__buttons .button.wc-forward {
    background-color: #fff;
    color: #333;
    border: 1px solid #dee2e6;
}

.widget_shopping_cart_content .woocommerce-mini-cart__buttons .button.wc-forward:hover {
    background-color: #f8f9fa;
    border-color: #adb5bd;
}

.widget_shopping_cart_content .woocommerce-mini-cart__buttons .button.checkout {
    background-color: #ff6000;
    color: white;
    border: 1px solid #ff6000;
}

.widget_shopping_cart_content .woocommerce-mini-cart__buttons .button.checkout:hover {
    background-color: #e55a00;
    border-color: #e55a00;
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(255, 96, 0, 0.3);
}

/* Bos sepet mesaji */
.widget_shopping_cart_content .woocommerce-mini-cart__empty-message {
    text-align: center;
    color: #666;
    font-style: italic;
    padding: 40px 20px;
    background-color: #f8f9fa;
    border-radius: 8px;
    margin: 0;
    border: 1px solid #e9ecef;
}

/* SIDEBAR TUTARLILIGI - TUM SAYFALARDA AYNI GORUNUM */
/* En yuksek oncelik ile tum CSS cakismalarini onle */
html body .cart-sidebar .widget_shopping_cart_content .woocommerce-mini-cart-item,
html body.home .cart-sidebar .widget_shopping_cart_content .woocommerce-mini-cart-item,
html body.shop .cart-sidebar .widget_shopping_cart_content .woocommerce-mini-cart-item,
html body.single-product .cart-sidebar .widget_shopping_cart_content .woocommerce-mini-cart-item,
html body.archive .cart-sidebar .widget_shopping_cart_content .woocommerce-mini-cart-item,
html body.woocommerce .cart-sidebar .widget_shopping_cart_content .woocommerce-mini-cart-item,
html body.woocommerce-shop .cart-sidebar .widget_shopping_cart_content .woocommerce-mini-cart-item,
html body.woocommerce-page .cart-sidebar .widget_shopping_cart_content .woocommerce-mini-cart-item {
    padding: 12px 0 !important;
    margin: 0 !important;
    border-bottom: 1px solid #f0f0f0 !important;
    display: flex !important;
    align-items: flex-start !important;
    position: relative !important;
    z-index: 1 !important;
}

/* Mini cart listesi icin tutarli stil */
html body .cart-sidebar .widget_shopping_cart_content .woocommerce-mini-cart {
    padding: 0 !important;
    margin: 0 !important;
    list-style: none !important;
}

/* Son elementin border'ini kaldir */
html body .cart-sidebar .widget_shopping_cart_content .woocommerce-mini-cart-item:last-child {
    border-bottom: none !important;
}

/* Responsive */
@media (max-width: 768px) {
    .cart-sidebar {
        width: 100vw;
        right: -100vw;
    }

    .cart-sidebar-content {
        width: 100%;
    }

    .cart-sidebar-body {
        padding: 15px;
        padding-bottom: 0;
        max-height: calc(100vh - 120px); /* Mobilde daha az header/footer */
    }

    .cart-sidebar-footer {
        padding: 15px;
    }

    .widget_shopping_cart_content .cart-footer {
        padding: 15px 0;
    }

    .widget_shopping_cart_content .woocommerce-mini-cart {
        max-height: calc(100vh - 280px) !important; /* Mobil icin ayarla */
    }

    html body .cart-sidebar .widget_shopping_cart_content .woocommerce-mini-cart-item img {
        width: 40px !important;
        height: 40px !important;
        margin-right: 10px !important;
    }

    html body .cart-sidebar .widget_shopping_cart_content .woocommerce-mini-cart-item {
        padding: 12px 0 !important;
    }

    /* User Actions Mobil Stilleri */
    .login-button {
        padding: 8px 12px;
        font-size: 13px;
        gap: 6px;
    }

    .login-icon {
        width: 14px;
        height: 14px;
    }

    .login-main {
        font-size: 13px;
    }

    .login-sub {
        font-size: 10px;
    }

    /* User Avatar Mobil Stilleri */
    .user-avatar-circle {
        width: 35px !important;
        height: 35px !important;
    }

    /* WooCommerce Avatar Koruma - Mobil */
    .woocommerce .user-avatar-circle,
    .woocommerce-page .user-avatar-circle {
        width: 35px !important;
        height: 35px !important;
    }

    .user-dropdown-menu {
        right: -10px;
        min-width: 200px;
    }

    .user-info {
        padding: 14px;
    }

    .user-name {
        font-size: 13px;
    }

    .user-email {
        font-size: 11px;
    }

    .user-menu-item {
        padding: 10px 14px;
        font-size: 13px;
        gap: 8px;
    }

    .user-menu-item .menu-icon {
        width: 14px;
        height: 14px;
    }
}

/* Footer Stilleri */
.site-footer {
    background-color: #2c3e50;
    color: #ecf0f1;
    padding: 40px 0;
    margin-top: 40px;
}

.footer-widgets {
    display: flex;
    justify-content: space-between;
    margin-bottom: 40px;
}

.widget-area {
    width: 30%;
}

.widget-area h3 {
    color: #fff;
    border-bottom: 2px solid #ff6000;
    padding-bottom: 10px;
    margin-bottom: 20px;
}

.widget-area ul {
    list-style: none;
    padding: 0;
    margin: 0;
}

.widget-area ul li a {
    color: #ecf0f1;
    text-decoration: none;
    line-height: 2;
    transition: color 0.3s;
}

.widget-area ul li a:hover {
    color: #ff6000;
}

.footer-bottom {
    text-align: center;
    border-top: 1px solid #34495e;
    padding-top: 20px;
}

.footer-bottom p {
    margin: 0;
    color: #bdc3c7;
}

/* Slider Stilleri */
.main-slider {
    width: 100%;
    max-width: 100%;
    height: 500px;
    margin-bottom: 40px;
    margin-top: 0;
    overflow: hidden; /* Taşan içeriği gizle */
    position: relative;
}

/* Swiper wrapper'ı temizle */
.main-slider .swiper-wrapper {
    margin: 0 !important;
    padding: 0 !important;
}

.swiper-slide {
    text-align: center;
    font-size: 18px;
    background: #fff;

    /* Center slide text vertically */
    display: -webkit-box;
    display: -ms-flexbox;
    display: -webkit-flex;
    display: flex;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    -webkit-justify-content: center;
    justify-content: center;
    -webkit-box-align: center;
    -ms-flex-align: center;
    -webkit-align-items: center;
    align-items: center;
}

.swiper-slide img {
    display: block;
    width: 100%;
    height: 100%;
    object-fit: cover;
    max-width: 100%; /* Görüntünün container'dan taşmasını engelle */
}

/* Slider içeriğindeki gereksiz boşlukları kaldır */
.swiper-slide p {
    margin: 0 !important;
    padding: 0 !important;
}

.swiper-slide br {
    display: none !important;
}

/* Video container içindeki boşlukları kaldır */
.swiper-slide .video-container p {
    margin: 0 !important;
    padding: 0 !important;
}

.swiper-slide .video-container br {
    display: none !important;
}

.video-container {
    position: relative;
    width: 100%;
    height: 100%;
    margin: 0 !important;
    padding: 0 !important;
}

.video-container iframe {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    margin: 0 !important;
    padding: 0 !important;
}

/* WordPress editöründen gelen gereksiz elementleri kaldır */
.video-container > p:empty,
.video-container > br,
.video-container > div:empty {
    display: none !important;
}

/* Slider içindeki tüm WordPress editör boşluklarını kaldır */
.swiper-slide > p:empty,
.swiper-slide > br:first-child,
.swiper-slide > br:last-child,
.swiper-slide > div:empty {
    display: none !important;
}

/* WordPress wpautop fonksiyonundan gelen gereksiz p etiketlerini kaldır */
.swiper-slide p:only-child:empty {
    display: none !important;
}

/* İlk ve son br etiketlerini kaldır */
.swiper-slide > br:first-of-type,
.swiper-slide > br:last-of-type {
    display: none !important;
}

/* WooCommerce Temel Stiller */
/* WooCommerce sayfalarinda header ile tutarli hizalama */
.woocommerce .site-main,
.woocommerce-page .site-main {
    margin: 0 auto;
    /* width: 75%; */ /* Header container ile ayni genislik */
    max-width: 1200px; /* Container ile ayni maksimum genislik */
}

/* WooCommerce content wrapper'i */
.woocommerce .content-area,
.woocommerce-page .content-area {
    width: 100% !important;
    margin: 0 !important;
    padding: 0 !important;
}

/* Urun sayfasi icin ozel hizalama */
.single-product .woocommerce {
    /* width: 75%; */
    max-width: 1200px;
    margin: 0 auto;
}

/* Mobil cihazlarda urun sayfasi */
@media (max-width: 768px) {
    .single-product .woocommerce {
        /* width: 95%; */
        padding: 0 10px;
    }
}

/* WooCommerce Ürün Grid - Responsive Grid */
.woocommerce ul.products {
    display: grid !important;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)) !important;
    gap: 20px !important;
    list-style: none !important;
    padding: 0 !important;
    margin: 0 !important;
}

/* Desktop icin 4 sutun */
@media (min-width: 1024px) {
    .woocommerce ul.products {
        grid-template-columns: repeat(4, 1fr) !important;
    }
}

/* Tablet icin 3 sutun */
@media (max-width: 1023px) and (min-width: 768px) {
    .woocommerce ul.products {
        grid-template-columns: repeat(3, 1fr) !important;
    }
}

/* Mobil icin 2 sutun */
@media (max-width: 767px) {
    .woocommerce ul.products {
        grid-template-columns: repeat(2, 1fr) !important;
        gap: 15px !important;
    }
}

.woocommerce ul.products li.product {
    width: auto !important;
    margin: 0 !important;
    float: none !important;
    clear: none !important;
    display: block !important;
}

/* WooCommerce pseudo-elementleri temizle */
.woocommerce .products ul::after,
.woocommerce .products ul::before,
.woocommerce ul.products::after,
.woocommerce ul.products::before {
    content: none !important;
    display: none !important;
}



/* Alttaki sonuc sayisini ve sıralama butonunu gizle - spesifik elementler */
#main > div.columns-3 > div > p,
#main > div.columns-3 > div > form {
    display: none !important;
}

/* Ürün Detay Sayfası */
.product-details-wrapper {
    display: flex;
    gap: 30px;
    margin-bottom: 30px;
}

.product .images {
    flex: 1;
}

.product .summary {
    flex: 1;
}

@media (max-width: 768px) {
    .product-details-wrapper {
        flex-direction: column;
        gap: 20px;
    }
}

/* WooCommerce Sepetim Butonlarini Gizle */
/* Sepete ekleme sonrasi cikan "Sepetim" butonlarini gizler */
.woocommerce ul.products li.product .added_to_cart,
.woocommerce-page ul.products li.product .added_to_cart,
.added_to_cart.wc-forward {
    display: none !important;
}

/* Header Responsive Stilleri */
@media (max-width: 768px) {
    .container {
        width: 95%; /* Mobilde daha genis kullanim */
        padding: 0 10px;
    }

    .header-top .container {
        flex-direction: column;
        gap: 15px;
        padding: 20px 10px;
    }

    .search-form-container {
        order: 3;
        margin: 0;
        width: 100%;
    }

    .header-right {
        order: 2;
        gap: 15px;
        width: 100%;
        justify-content: space-between;
    }

    .logo {
        order: 1;
    }

    .education-panel {
        display: none; /* Mobilde egitim paneli gizle */
    }

    /* Dropdown menu mobil ayarları */
    .main-navigation ul li:not(.has-mega-menu) .sub-menu {
        position: static;
        box-shadow: none;
        border: none;
        border-radius: 0;
        background-color: #f8f9fa;
        margin-top: 5px;
        padding: 0;
    }

    .main-navigation ul li:not(.has-mega-menu) .sub-menu li .sub-menu {
        margin-left: 15px;
        border-left: 2px solid #e0e0e0;
        padding-left: 10px;
    }

    /* WooCommerce mobil hizalama */
    .woocommerce .site-main,
    .woocommerce-page .site-main {
        width: 95%;
        padding: 0 10px;
    }

    /* Slider mobil ayarları */
    .main-slider {
        height: 300px; /* Mobilde daha kısa yükseklik */
        margin-bottom: 20px;
        margin-top: 0;
    }

    /* Mobilde slider içeriğini temizle */
    .swiper-slide p,
    .swiper-slide br {
        display: none !important;
    }
}

@media (max-width: 480px) {
    .header-right {
        gap: 10px;
    }

    .login-button {
        padding: 6px 10px;
        font-size: 12px;
        gap: 4px;
    }

    .login-icon {
        width: 12px;
        height: 12px;
    }

    .login-sub {
        display: none; /* Mobilde "veya uye ol" metnini gizle */
    }

    .user-avatar-circle {
        width: 32px !important;
        height: 32px !important;
    }

    /* WooCommerce Avatar Koruma - Cok Kucuk Ekranlar */
    .woocommerce .user-avatar-circle,
    .woocommerce-page .user-avatar-circle {
        width: 32px !important;
        height: 32px !important;
    }
}

/* Main Header H1 Stilleri */
#main > header > h1 {
    margin-bottom: 10px;
    margin-top: 35px;
}

/* Post-15 navigasyon sol yeşil boşluğunu kaldır */
#post-15 > div > div > nav > ul {
    border-left: none !important;
    margin-left: 0 !important;
    padding-left: 0 !important;
}
